"use strict";
exports.uz = void 0;
var _index = require("./uz/_lib/formatDistance.js");
var _index2 = require("./uz/_lib/formatLong.js");
var _index3 = require("./uz/_lib/formatRelative.js");
var _index4 = require("./uz/_lib/localize.js");
var _index5 = require("./uz/_lib/match.js");

/**
 * @category Locales
 * @summary Uzbek locale.
 * @language Uzbek
 * @iso-639-2 uzb
 * <AUTHOR> [@mukhammadali](https://github.com/Mukhammadali)
 */
const uz = (exports.uz = {
  code: "uz",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
