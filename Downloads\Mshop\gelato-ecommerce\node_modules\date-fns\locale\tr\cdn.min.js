var q=function(J){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},q(J)},W=function(J,G){var T=Object.keys(J);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(J);G&&(Y=Y.filter(function(K){return Object.getOwnPropertyDescriptor(J,K).enumerable})),T.push.apply(T,Y)}return T},z=function(J){for(var G=1;G<arguments.length;G++){var T=arguments[G]!=null?arguments[G]:{};G%2?W(Object(T),!0).forEach(function(Y){CC(J,Y,T[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(T)):W(Object(T)).forEach(function(Y){Object.defineProperty(J,Y,Object.getOwnPropertyDescriptor(T,Y))})}return J},CC=function(J,G,T){if(G=HC(G),G in J)Object.defineProperty(J,G,{value:T,enumerable:!0,configurable:!0,writable:!0});else J[G]=T;return J},HC=function(J){var G=BC(J,"string");return q(G)=="symbol"?G:String(G)},BC=function(J,G){if(q(J)!="object"||!J)return J;var T=J[Symbol.toPrimitive];if(T!==void 0){var Y=T.call(J,G||"default");if(q(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(J)};(function(J){var G=Object.defineProperty,T=function C(B,H){for(var E in H)G(B,E,{get:H[E],enumerable:!0,configurable:!0,set:function U(X){return H[E]=function(){return X}}})},Y={lessThanXSeconds:{one:"bir saniyeden az",other:"{{count}} saniyeden az"},xSeconds:{one:"1 saniye",other:"{{count}} saniye"},halfAMinute:"yar\u0131m dakika",lessThanXMinutes:{one:"bir dakikadan az",other:"{{count}} dakikadan az"},xMinutes:{one:"1 dakika",other:"{{count}} dakika"},aboutXHours:{one:"yakla\u015F\u0131k 1 saat",other:"yakla\u015F\u0131k {{count}} saat"},xHours:{one:"1 saat",other:"{{count}} saat"},xDays:{one:"1 g\xFCn",other:"{{count}} g\xFCn"},aboutXWeeks:{one:"yakla\u015F\u0131k 1 hafta",other:"yakla\u015F\u0131k {{count}} hafta"},xWeeks:{one:"1 hafta",other:"{{count}} hafta"},aboutXMonths:{one:"yakla\u015F\u0131k 1 ay",other:"yakla\u015F\u0131k {{count}} ay"},xMonths:{one:"1 ay",other:"{{count}} ay"},aboutXYears:{one:"yakla\u015F\u0131k 1 y\u0131l",other:"yakla\u015F\u0131k {{count}} y\u0131l"},xYears:{one:"1 y\u0131l",other:"{{count}} y\u0131l"},overXYears:{one:"1 y\u0131ldan fazla",other:"{{count}} y\u0131ldan fazla"},almostXYears:{one:"neredeyse 1 y\u0131l",other:"neredeyse {{count}} y\u0131l"}},K=function C(B,H,E){var U,X=Y[B];if(typeof X==="string")U=X;else if(H===1)U=X.one;else U=X.other.replace("{{count}}",H.toString());if(E!==null&&E!==void 0&&E.addSuffix)if(E.comparison&&E.comparison>0)return U+" sonra";else return U+" \xF6nce";return U};function N(C){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=B.width?String(B.width):C.defaultWidth,E=C.formats[H]||C.formats[C.defaultWidth];return E}}var $={full:"d MMMM y EEEE",long:"d MMMM y",medium:"d MMM y",short:"dd.MM.yyyy"},M={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} 'saat' {{time}}",long:"{{date}} 'saat' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:N({formats:$,defaultWidth:"full"}),time:N({formats:M,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},L={lastWeek:"'ge\xE7en hafta' eeee 'saat' p",yesterday:"'d\xFCn saat' p",today:"'bug\xFCn saat' p",tomorrow:"'yar\u0131n saat' p",nextWeek:"eeee 'saat' p",other:"P"},V=function C(B,H,E,U){return L[B]};function O(C){return function(B,H){var E=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",U;if(E==="formatting"&&C.formattingValues){var X=C.defaultFormattingWidth||C.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):X;U=C.formattingValues[Z]||C.formattingValues[X]}else{var I=C.defaultWidth,x=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;U=C.values[x]||C.values[I]}var A=C.argumentCallback?C.argumentCallback(B):B;return U[A]}}var f={narrow:["M\xD6","MS"],abbreviated:["M\xD6","MS"],wide:["Milattan \xD6nce","Milattan Sonra"]},j={narrow:["1","2","3","4"],abbreviated:["1\xC7","2\xC7","3\xC7","4\xC7"],wide:["\u0130lk \xE7eyrek","\u0130kinci \xC7eyrek","\xDC\xE7\xFCnc\xFC \xE7eyrek","Son \xE7eyrek"]},F={narrow:["O","\u015E","M","N","M","H","T","A","E","E","K","A"],abbreviated:["Oca","\u015Eub","Mar","Nis","May","Haz","Tem","A\u011Fu","Eyl","Eki","Kas","Ara"],wide:["Ocak","\u015Eubat","Mart","Nisan","May\u0131s","Haziran","Temmuz","A\u011Fustos","Eyl\xFCl","Ekim","Kas\u0131m","Aral\u0131k"]},_={narrow:["P","P","S","\xC7","P","C","C"],short:["Pz","Pt","Sa","\xC7a","Pe","Cu","Ct"],abbreviated:["Paz","Pzt","Sal","\xC7ar","Per","Cum","Cts"],wide:["Pazar","Pazartesi","Sal\u0131","\xC7ar\u015Famba","Per\u015Fembe","Cuma","Cumartesi"]},v={narrow:{am:"\xF6\xF6",pm:"\xF6s",midnight:"gy",noon:"\xF6",morning:"sa",afternoon:"\xF6s",evening:"ak",night:"ge"},abbreviated:{am:"\xD6\xD6",pm:"\xD6S",midnight:"gece yar\u0131s\u0131",noon:"\xF6\u011Fle",morning:"sabah",afternoon:"\xF6\u011Fleden sonra",evening:"ak\u015Fam",night:"gece"},wide:{am:"\xD6.\xD6.",pm:"\xD6.S.",midnight:"gece yar\u0131s\u0131",noon:"\xF6\u011Fle",morning:"sabah",afternoon:"\xF6\u011Fleden sonra",evening:"ak\u015Fam",night:"gece"}},w={narrow:{am:"\xF6\xF6",pm:"\xF6s",midnight:"gy",noon:"\xF6",morning:"sa",afternoon:"\xF6s",evening:"ak",night:"ge"},abbreviated:{am:"\xD6\xD6",pm:"\xD6S",midnight:"gece yar\u0131s\u0131",noon:"\xF6\u011Flen",morning:"sabahleyin",afternoon:"\xF6\u011Fleden sonra",evening:"ak\u015Famleyin",night:"geceleyin"},wide:{am:"\xF6.\xF6.",pm:"\xF6.s.",midnight:"gece yar\u0131s\u0131",noon:"\xF6\u011Flen",morning:"sabahleyin",afternoon:"\xF6\u011Fleden sonra",evening:"ak\u015Famleyin",night:"geceleyin"}},P=function C(B,H){var E=Number(B);return E+"."},k={ordinalNumber:P,era:O({values:f,defaultWidth:"wide"}),quarter:O({values:j,defaultWidth:"wide",argumentCallback:function C(B){return Number(B)-1}}),month:O({values:F,defaultWidth:"wide"}),day:O({values:_,defaultWidth:"wide"}),dayPeriod:O({values:v,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"})};function Q(C){return function(B){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=H.width,U=E&&C.matchPatterns[E]||C.matchPatterns[C.defaultMatchWidth],X=B.match(U);if(!X)return null;var Z=X[0],I=E&&C.parsePatterns[E]||C.parsePatterns[C.defaultParseWidth],x=Array.isArray(I)?h(I,function(D){return D.test(Z)}):b(I,function(D){return D.test(Z)}),A;A=C.valueCallback?C.valueCallback(x):x,A=H.valueCallback?H.valueCallback(A):A;var t=B.slice(Z.length);return{value:A,rest:t}}}var b=function C(B,H){for(var E in B)if(Object.prototype.hasOwnProperty.call(B,E)&&H(B[E]))return E;return},h=function C(B,H){for(var E=0;E<B.length;E++)if(H(B[E]))return E;return};function m(C){return function(B){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=B.match(C.matchPattern);if(!E)return null;var U=E[0],X=B.match(C.parsePattern);if(!X)return null;var Z=C.valueCallback?C.valueCallback(X[0]):X[0];Z=H.valueCallback?H.valueCallback(Z):Z;var I=B.slice(U.length);return{value:Z,rest:I}}}var c=/^(\d+)(\.)?/i,y=/\d+/i,p={narrow:/^(mö|ms)/i,abbreviated:/^(mö|ms)/i,wide:/^(milattan önce|milattan sonra)/i},u={any:[/(^mö|^milattan önce)/i,/(^ms|^milattan sonra)/i]},g={narrow:/^[1234]/i,abbreviated:/^[1234]ç/i,wide:/^((i|İ)lk|(i|İ)kinci|üçüncü|son) çeyrek/i},d={any:[/1/i,/2/i,/3/i,/4/i],abbreviated:[/1ç/i,/2ç/i,/3ç/i,/4ç/i],wide:[/^(i|İ)lk çeyrek/i,/(i|İ)kinci çeyrek/i,/üçüncü çeyrek/i,/son çeyrek/i]},l={narrow:/^[oşmnhtaek]/i,abbreviated:/^(oca|şub|mar|nis|may|haz|tem|ağu|eyl|eki|kas|ara)/i,wide:/^(ocak|şubat|mart|nisan|mayıs|haziran|temmuz|ağustos|eylül|ekim|kasım|aralık)/i},i={narrow:[/^o/i,/^ş/i,/^m/i,/^n/i,/^m/i,/^h/i,/^t/i,/^a/i,/^e/i,/^e/i,/^k/i,/^a/i],any:[/^o/i,/^ş/i,/^mar/i,/^n/i,/^may/i,/^h/i,/^t/i,/^ağ/i,/^ey/i,/^ek/i,/^k/i,/^ar/i]},n={narrow:/^[psçc]/i,short:/^(pz|pt|sa|ça|pe|cu|ct)/i,abbreviated:/^(paz|pzt|sal|çar|per|cum|cts)/i,wide:/^(pazar(?!tesi)|pazartesi|salı|çarşamba|perşembe|cuma(?!rtesi)|cumartesi)/i},s={narrow:[/^p/i,/^p/i,/^s/i,/^ç/i,/^p/i,/^c/i,/^c/i],any:[/^pz/i,/^pt/i,/^sa/i,/^ça/i,/^pe/i,/^cu/i,/^ct/i],wide:[/^pazar(?!tesi)/i,/^pazartesi/i,/^salı/i,/^çarşamba/i,/^perşembe/i,/^cuma(?!rtesi)/i,/^cumartesi/i]},o={narrow:/^(öö|ös|gy|ö|sa|ös|ak|ge)/i,any:/^(ö\.?\s?[ös]\.?|öğleden sonra|gece yarısı|öğle|(sabah|öğ|akşam|gece)(leyin))/i},r={any:{am:/^ö\.?ö\.?/i,pm:/^ö\.?s\.?/i,midnight:/^(gy|gece yarısı)/i,noon:/^öğ/i,morning:/^sa/i,afternoon:/^öğleden sonra/i,evening:/^ak/i,night:/^ge/i}},e={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function C(B){return parseInt(B,10)}}),era:Q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function C(B){return B+1}}),month:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"tr",formatDistance:K,formatLong:R,formatRelative:V,localize:k,match:e,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=z(z({},window.dateFns),{},{locale:z(z({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{tr:a})})})();

//# debugId=B7D445788C1A239E64756e2164756e21
