"use strict";
exports.enCA = void 0;
var _index = require("./en-US/_lib/formatRelative.js");
var _index2 = require("./en-US/_lib/localize.js");
var _index3 = require("./en-US/_lib/match.js");

var _index4 = require("./en-CA/_lib/formatDistance.js");
var _index5 = require("./en-CA/_lib/formatLong.js");

/**
 * @category Locales
 * @summary English locale (Canada).
 * @language English
 * @iso-639-2 eng
 * <AUTHOR> [@markowsiak](https://github.com/markowsiak)
 * <AUTHOR> [@mimperatore](https://github.com/mimperatore)
 */
const enCA = (exports.enCA = {
  code: "en-CA",
  formatDistance: _index4.formatDistance,
  formatLong: _index5.formatLong,
  formatRelative: _index.formatRelative,
  localize: _index2.localize,
  match: _index3.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
