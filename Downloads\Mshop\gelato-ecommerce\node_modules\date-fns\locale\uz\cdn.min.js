var A=function(U){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},A(U)},S=function(U,J){var X=Object.keys(U);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(U);J&&(I=I.filter(function(N){return Object.getOwnPropertyDescriptor(U,N).enumerable})),X.push.apply(X,I)}return X},x=function(U){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?S(Object(X),!0).forEach(function(I){H1(U,I,X[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(U,Object.getOwnPropertyDescriptors(X)):S(Object(X)).forEach(function(I){Object.defineProperty(U,I,Object.getOwnPropertyDescriptor(X,I))})}return U},H1=function(U,J,X){if(J=B1(J),J in U)Object.defineProperty(U,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else U[J]=X;return U},B1=function(U){var J=C1(U,"string");return A(J)=="symbol"?J:String(J)},C1=function(U,J){if(A(U)!="object"||!U)return U;var X=U[Symbol.toPrimitive];if(X!==void 0){var I=X.call(U,J||"default");if(A(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(U)};(function(U){var J=Object.defineProperty,X=function H(C,B){for(var G in B)J(C,G,{get:B[G],enumerable:!0,configurable:!0,set:function Y(Z){return B[G]=function(){return Z}}})},I={lessThanXSeconds:{one:"sekunddan kam",other:"{{count}} sekunddan kam"},xSeconds:{one:"1 sekund",other:"{{count}} sekund"},halfAMinute:"yarim minut",lessThanXMinutes:{one:"bir minutdan kam",other:"{{count}} minutdan kam"},xMinutes:{one:"1 minut",other:"{{count}} minut"},aboutXHours:{one:"tahminan 1 soat",other:"tahminan {{count}} soat"},xHours:{one:"1 soat",other:"{{count}} soat"},xDays:{one:"1 kun",other:"{{count}} kun"},aboutXWeeks:{one:"tahminan 1 hafta",other:"tahminan {{count}} hafta"},xWeeks:{one:"1 hafta",other:"{{count}} hafta"},aboutXMonths:{one:"tahminan 1 oy",other:"tahminan {{count}} oy"},xMonths:{one:"1 oy",other:"{{count}} oy"},aboutXYears:{one:"tahminan 1 yil",other:"tahminan {{count}} yil"},xYears:{one:"1 yil",other:"{{count}} yil"},overXYears:{one:"1 yildan ko'p",other:"{{count}} yildan ko'p"},almostXYears:{one:"deyarli 1 yil",other:"deyarli {{count}} yil"}},N=function H(C,B,G){var Y,Z=I[C];if(typeof Z==="string")Y=Z;else if(B===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(B));if(G!==null&&G!==void 0&&G.addSuffix)if(G.comparison&&G.comparison>0)return Y+" dan keyin";else return Y+" oldin";return Y};function z(H){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=C.width?String(C.width):H.defaultWidth,G=H.formats[B]||H.formats[H.defaultWidth];return G}}var W={full:"EEEE, do MMMM, y",long:"do MMMM, y",medium:"d MMM, y",short:"dd/MM/yyyy"},$={full:"h:mm:ss zzzz",long:"h:mm:ss z",medium:"h:mm:ss",short:"h:mm"},M={any:"{{date}}, {{time}}"},R={date:z({formats:W,defaultWidth:"full"}),time:z({formats:$,defaultWidth:"full"}),dateTime:z({formats:M,defaultWidth:"any"})},L={lastWeek:"'oldingi' eeee p 'da'",yesterday:"'kecha' p 'da'",today:"'bugun' p 'da'",tomorrow:"'ertaga' p 'da'",nextWeek:"eeee p 'da'",other:"P"},V=function H(C,B,G,Y){return L[C]};function Q(H){return function(C,B){var G=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",Y;if(G==="formatting"&&H.formattingValues){var Z=H.defaultFormattingWidth||H.defaultWidth,T=B!==null&&B!==void 0&&B.width?String(B.width):Z;Y=H.formattingValues[T]||H.formattingValues[Z]}else{var E=H.defaultWidth,K=B!==null&&B!==void 0&&B.width?String(B.width):H.defaultWidth;Y=H.values[K]||H.values[E]}var O=H.argumentCallback?H.argumentCallback(C):C;return Y[O]}}var f={narrow:["M.A","M."],abbreviated:["M.A","M."],wide:["Miloddan Avvalgi","Milodiy"]},j={narrow:["1","2","3","4"],abbreviated:["CH.1","CH.2","CH.3","CH.4"],wide:["1-chi chorak","2-chi chorak","3-chi chorak","4-chi chorak"]},v={narrow:["Y","F","M","A","M","I","I","A","S","O","N","D"],abbreviated:["Yan","Fev","Mar","Apr","May","Iyun","Iyul","Avg","Sen","Okt","Noy","Dek"],wide:["Yanvar","Fevral","Mart","Aprel","May","Iyun","Iyul","Avgust","Sentabr","Oktabr","Noyabr","Dekabr"]},_={narrow:["Y","D","S","CH","P","J","SH"],short:["Ya","Du","Se","Cho","Pa","Ju","Sha"],abbreviated:["Yak","Dush","Sesh","Chor","Pay","Jum","Shan"],wide:["Yakshanba","Dushanba","Seshanba","Chorshanba","Payshanba","Juma","Shanba"]},P={narrow:{am:"a",pm:"p",midnight:"y.t",noon:"p.",morning:"ertalab",afternoon:"tushdan keyin",evening:"kechqurun",night:"tun"},abbreviated:{am:"AM",pm:"PM",midnight:"yarim tun",noon:"peshin",morning:"ertalab",afternoon:"tushdan keyin",evening:"kechqurun",night:"tun"},wide:{am:"a.m.",pm:"p.m.",midnight:"yarim tun",noon:"peshin",morning:"ertalab",afternoon:"tushdan keyin",evening:"kechqurun",night:"tun"}},w={narrow:{am:"a",pm:"p",midnight:"y.t",noon:"p.",morning:"ertalab",afternoon:"tushdan keyin",evening:"kechqurun",night:"tun"},abbreviated:{am:"AM",pm:"PM",midnight:"yarim tun",noon:"peshin",morning:"ertalab",afternoon:"tushdan keyin",evening:"kechqurun",night:"tun"},wide:{am:"a.m.",pm:"p.m.",midnight:"yarim tun",noon:"peshin",morning:"ertalab",afternoon:"tushdan keyin",evening:"kechqurun",night:"tun"}},F=function H(C,B){return String(C)},b={ordinalNumber:F,era:Q({values:f,defaultWidth:"wide"}),quarter:Q({values:j,defaultWidth:"wide",argumentCallback:function H(C){return C-1}}),month:Q({values:v,defaultWidth:"wide"}),day:Q({values:_,defaultWidth:"wide"}),dayPeriod:Q({values:P,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"})};function q(H){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=B.width,Y=G&&H.matchPatterns[G]||H.matchPatterns[H.defaultMatchWidth],Z=C.match(Y);if(!Z)return null;var T=Z[0],E=G&&H.parsePatterns[G]||H.parsePatterns[H.defaultParseWidth],K=Array.isArray(E)?h(E,function(D){return D.test(T)}):k(E,function(D){return D.test(T)}),O;O=H.valueCallback?H.valueCallback(K):K,O=B.valueCallback?B.valueCallback(O):O;var t=C.slice(T.length);return{value:O,rest:t}}}var k=function H(C,B){for(var G in C)if(Object.prototype.hasOwnProperty.call(C,G)&&B(C[G]))return G;return},h=function H(C,B){for(var G=0;G<C.length;G++)if(B(C[G]))return G;return};function m(H){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=C.match(H.matchPattern);if(!G)return null;var Y=G[0],Z=C.match(H.parsePattern);if(!Z)return null;var T=H.valueCallback?H.valueCallback(Z[0]):Z[0];T=B.valueCallback?B.valueCallback(T):T;var E=C.slice(Y.length);return{value:T,rest:E}}}var c=/^(\d+)(chi)?/i,y=/\d+/i,p={narrow:/^(m\.a|m\.)/i,abbreviated:/^(m\.a\.?\s?m\.?)/i,wide:/^(miloddan avval|miloddan keyin)/i},d={any:[/^b/i,/^(a|c)/i]},g={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](chi)? chorak/i},u={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[yfmasond]/i,abbreviated:/^(yan|fev|mar|apr|may|iyun|iyul|avg|sen|okt|noy|dek)/i,wide:/^(yanvar|fevral|mart|aprel|may|iyun|iyul|avgust|sentabr|oktabr|noyabr|dekabr)/i},i={narrow:[/^y/i,/^f/i,/^m/i,/^a/i,/^m/i,/^i/i,/^i/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ya/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^iyun/i,/^iyul/i,/^av/i,/^s/i,/^o/i,/^n/i,/^d/i]},n={narrow:/^[ydschj]/i,short:/^(ya|du|se|cho|pa|ju|sha)/i,abbreviated:/^(yak|dush|sesh|chor|pay|jum|shan)/i,wide:/^(yakshanba|dushanba|seshanba|chorshanba|payshanba|juma|shanba)/i},s={narrow:[/^y/i,/^d/i,/^s/i,/^ch/i,/^p/i,/^j/i,/^sh/i],any:[/^ya/i,/^d/i,/^se/i,/^ch/i,/^p/i,/^j/i,/^sh/i]},o={narrow:/^(a|p|y\.t|p| (ertalab|tushdan keyin|kechqurun|tun))/i,any:/^([ap]\.?\s?m\.?|yarim tun|peshin| (ertalab|tushdan keyin|kechqurun|tun))/i},r={any:{am:/^a/i,pm:/^p/i,midnight:/^y\.t/i,noon:/^pe/i,morning:/ertalab/i,afternoon:/tushdan keyin/i,evening:/kechqurun/i,night:/tun/i}},a={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function H(C){return parseInt(C,10)}}),era:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),quarter:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any",valueCallback:function H(C){return C+1}}),month:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"uz",formatDistance:N,formatLong:R,formatRelative:V,localize:b,match:a,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(U=window.dateFns)===null||U===void 0?void 0:U.locale),{},{uz:e})})})();

//# debugId=FB9A515A85971F8F64756e2164756e21
