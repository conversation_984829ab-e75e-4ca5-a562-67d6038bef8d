"use strict";
exports.is = void 0;
var _index = require("./is/_lib/formatDistance.js");
var _index2 = require("./is/_lib/formatLong.js");
var _index3 = require("./is/_lib/formatRelative.js");
var _index4 = require("./is/_lib/localize.js");
var _index5 = require("./is/_lib/match.js");

/**
 * @category Locales
 * @summary Icelandic locale.
 * @language Icelandic
 * @iso-639-2 isl
 * <AUTHOR> [@derekblank](https://github.com/derekblank)
 * <AUTHOR> Ýmir [@lamayg](https://github.com/lamayg)
 */
const is = (exports.is = {
  code: "is",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
