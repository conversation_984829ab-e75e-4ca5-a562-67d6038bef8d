"use strict";
exports.sq = void 0;
var _index = require("./sq/_lib/formatDistance.js");
var _index2 = require("./sq/_lib/formatLong.js");
var _index3 = require("./sq/_lib/formatRelative.js");
var _index4 = require("./sq/_lib/localize.js");
var _index5 = require("./sq/_lib/match.js");

/**
 * @category Locales
 * @summary Albanian locale.
 * @language Shqip
 * @iso-639-2 sqi
 * <AUTHOR> <PERSON> [@arditdine](https://github.com/arditdine)
 */
const sq = (exports.sq = {
  code: "sq",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
