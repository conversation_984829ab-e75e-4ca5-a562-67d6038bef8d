"use strict";
exports.sl = void 0;
var _index = require("./sl/_lib/formatDistance.js");
var _index2 = require("./sl/_lib/formatLong.js");
var _index3 = require("./sl/_lib/formatRelative.js");
var _index4 = require("./sl/_lib/localize.js");
var _index5 = require("./sl/_lib/match.js");

/**
 * @category Locales
 * @summary Slovenian locale.
 * @language Slovenian
 * @iso-639-2 slv
 * <AUTHOR> [@Neoglyph](https://github.com/Neoglyph)
 * <AUTHOR> [@mzgajner](https://github.com/mzgajner)
 */
const sl = (exports.sl = {
  code: "sl",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
