"use strict";
exports.nb = void 0;
var _index = require("./nb/_lib/formatDistance.js");
var _index2 = require("./nb/_lib/formatLong.js");
var _index3 = require("./nb/_lib/formatRelative.js");
var _index4 = require("./nb/_lib/localize.js");
var _index5 = require("./nb/_lib/match.js");

/**
 * @category Locales
 * @summary Norwegian Bokmål locale.
 * @language Norwegian Bokmål
 * @iso-639-2 nob
 * <AUTHOR> [@Hanse](https://github.com/Hanse)
 * <AUTHOR> [@mikolajgrzyb](https://github.com/mikolajgrzyb)
 * <AUTHOR> [@dagstuan](https://github.com/dagstuan)
 */
const nb = (exports.nb = {
  code: "nb",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
