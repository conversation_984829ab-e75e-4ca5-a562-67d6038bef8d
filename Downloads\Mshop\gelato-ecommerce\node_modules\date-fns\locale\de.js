"use strict";
exports.de = void 0;
var _index = require("./de/_lib/formatDistance.js");
var _index2 = require("./de/_lib/formatLong.js");
var _index3 = require("./de/_lib/formatRelative.js");
var _index4 = require("./de/_lib/localize.js");
var _index5 = require("./de/_lib/match.js");

/**
 * @category Locales
 * @summary German locale.
 * @language German
 * @iso-639-2 deu
 * <AUTHOR> [@DeMuu](https://github.com/DeMuu)
 * <AUTHOR> [@asia-t](https://github.com/asia-t)
 * <AUTHOR> [@vanvuongngo](https://github.com/vanvuongngo)
 * <AUTHOR> [@pex](https://github.com/pex)
 * <AUTHOR> [@Philipp91](https://github.com/Philipp91)
 */
const de = (exports.de = {
  code: "de",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
