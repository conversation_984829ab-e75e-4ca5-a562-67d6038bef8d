"use strict";
exports.ta = void 0;
var _index = require("./ta/_lib/formatDistance.js");
var _index2 = require("./ta/_lib/formatLong.js");
var _index3 = require("./ta/_lib/formatRelative.js");
var _index4 = require("./ta/_lib/localize.js");
var _index5 = require("./ta/_lib/match.js");

/**
 * @category Locales
 * @summary Tamil locale (India).
 * @language Tamil
 * @iso-639-2 tam
 * <AUTHOR> [@sibiraj-s](https://github.com/sibiraj-s)
 */
const ta = (exports.ta = {
  code: "ta",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
