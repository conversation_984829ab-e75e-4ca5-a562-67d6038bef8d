"use strict";
exports.ro = void 0;
var _index = require("./ro/_lib/formatDistance.js");
var _index2 = require("./ro/_lib/formatLong.js");
var _index3 = require("./ro/_lib/formatRelative.js");
var _index4 = require("./ro/_lib/localize.js");
var _index5 = require("./ro/_lib/match.js");

/**
 * @category Locales
 * @summary Romanian locale.
 * @language Romanian
 * @iso-639-2 ron
 * <AUTHOR> [@jsergiu](https://github.com/jsergiu)
 * <AUTHOR> [@aocneanu](https://github.com/aocneanu)
 * <AUTHOR> [@gandesc](https://github.com/gandesc)
 */
const ro = (exports.ro = {
  code: "ro",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
