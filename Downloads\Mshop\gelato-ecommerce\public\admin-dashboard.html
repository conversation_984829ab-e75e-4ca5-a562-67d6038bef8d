
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GELATO PREMIUM - 管理系統</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #1A1A1A;
            --secondary: #2C3E50;
            --accent: #E67E22;
            --success: #27AE60;
            --warning: #F39C12;
            --danger: #E74C3C;
            --info: #3498DB;
            --dark: #0C0C0C;
            --gray: #95A5A6;
            --light-gray: #BDC3C7;
            --bg: #F8F9FA;
            --white: #FFFFFF;
            --border: #E0E0E0;
        }

        body {
            font-family: 'Inter', 'Noto Sans TC', sans-serif;
            background-color: var(--bg);
            color: var(--primary);
            line-height: 1.6;
        }

        /* 登入頁面 - 極簡設計 */
        .login-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary);
            padding: 1rem;
        }

        .login-card {
            background: var(--white);
            width: 100%;
            max-width: 400px;
            padding: 3rem 2rem;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }

        .login-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .login-title {
            font-size: 1.8rem;
            font-weight: 700;
            letter-spacing: -0.5px;
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: var(--gray);
            font-size: 0.95rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--secondary);
            margin-bottom: 0.5rem;
            letter-spacing: 0.3px;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--border);
            background: var(--bg);
            font-size: 1rem;
            transition: all 0.2s;
            font-family: inherit;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--accent);
            background: var(--white);
        }

        .login-button {
            width: 100%;
            padding: 0.875rem;
            background: var(--primary);
            color: var(--white);
            border: none;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            letter-spacing: 0.5px;
        }

        .login-button:hover {
            background: var(--accent);
            transform: translateY(-1px);
        }

        .login-note {
            text-align: center;
            margin-top: 2rem;
            font-size: 0.875rem;
            color: var(--gray);
        }

        /* 主要佈局 */
        .dashboard {
            display: none;
            min-height: 100vh;
            background: var(--bg);
        }

        /* 頂部導航 */
        .topbar {
            background: var(--white);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 2rem;
            box-shadow: 0 1px 0 var(--border);
            z-index: 100;
        }

        .topbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--primary);
        }

        .brand {
            font-size: 1.25rem;
            font-weight: 700;
            letter-spacing: -0.5px;
            display: flex;
            flex-direction: column;
            line-height: 1.2;
        }

        .brand-subtitle {
            font-size: 0.65rem;
            font-weight: 300;
            letter-spacing: 0.5px;
            opacity: 0.6;
            margin-top: 2px;
        }

        .topbar-right {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            background: var(--primary);
            color: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .user-name {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .logout-link {
            color: var(--gray);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.2s;
        }

        .logout-link:hover {
            color: var(--danger);
        }

        /* 側邊欄 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 240px;
            background: var(--white);
            border-right: 1px solid var(--border);
            padding: 2rem 0;
            overflow-y: auto;
            transition: transform 0.3s;
            z-index: 999;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.25rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 2rem;
            color: var(--secondary);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s;
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary);
            background: var(--bg);
        }

        .nav-link.active {
            color: var(--accent);
            background: rgba(230, 126, 34, 0.1);
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--accent);
        }

        .nav-icon {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        /* 主要內容 */
        .main-content {
            margin-left: 240px;
            padding: 60px 0 0;
            min-height: 100vh;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            letter-spacing: -0.5px;
            margin-bottom: 0.25rem;
        }

        .page-subtitle {
            color: var(--gray);
            font-size: 0.95rem;
        }

        /* 統計卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--white);
            padding: 1.5rem;
            border: 1px solid var(--border);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
        }

        .stat-card.primary::before { background: var(--accent); }
        .stat-card.success::before { background: var(--success); }
        .stat-card.warning::before { background: var(--warning); }
        .stat-card.info::before { background: var(--info); }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.875rem;
            color: var(--gray);
            font-weight: 500;
            letter-spacing: 0.3px;
        }

        .stat-icon {
            font-size: 1.25rem;
            opacity: 0.3;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            line-height: 1;
            letter-spacing: -1px;
        }

        .stat-change {
            font-size: 0.75rem;
            margin-top: 0.5rem;
        }

        .stat-change.positive { color: var(--success); }
        .stat-change.negative { color: var(--danger); }

        /* 資料表格 */
        .data-card {
            background: var(--white);
            border: 1px solid var(--border);
            margin-bottom: 2rem;
            overflow: visible;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            letter-spacing: -0.3px;
        }

        .filter-group {
            display: flex;
            gap: 0.5rem;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            background: transparent;
            border: 1px solid var(--border);
            color: var(--secondary);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-btn:hover {
            border-color: var(--accent);
            color: var(--accent);
        }

        .filter-btn.active {
            background: var(--accent);
            border-color: var(--accent);
            color: var(--white);
        }

        /* 表格樣式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            text-align: left;
            padding: 1rem 1.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--gray);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: var(--bg);
            border-bottom: 1px solid var(--border);
        }

        .data-table td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border);
            font-size: 0.9rem;
            position: relative;
            overflow: visible;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .data-table tbody tr {
            transition: background 0.2s;
        }

        .data-table tbody tr:hover {
            background: var(--bg);
        }

        /* 狀態標籤 */
        .status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            letter-spacing: 0.3px;
            text-transform: uppercase;
        }

        .status.pending {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning);
        }

        .status.confirmed {
            background: rgba(52, 152, 219, 0.1);
            color: var(--info);
        }

        .status.preparing {
            background: rgba(155, 89, 182, 0.1);
            color: #9B59B6;
        }

        .status.ready {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success);
        }

        .status.delivered {
            background: rgba(149, 165, 166, 0.1);
            color: var(--gray);
        }

        /* 操作按鈕 */
        .action-group {
            position: relative;
            display: inline-block;
        }

        .action-menu-btn {
            padding: 0.5rem;
            background: transparent;
            border: none;
            color: var(--gray);
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 4px;
        }

        .action-menu-btn:hover {
            background: var(--bg);
            color: var(--primary);
        }

        .action-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            margin-top: 0.25rem;
            background: var(--white);
            border: 1px solid var(--border);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            min-width: 160px;
            z-index: 1000;
        }

        .action-menu.show {
            display: block;
        }

        /* 最後幾行的選單向上展開 */
        tr:nth-last-child(-n+3) .action-menu {
            top: auto;
            bottom: 100%;
            margin-bottom: 0.25rem;
            margin-top: 0;
        }

        .action-menu-item {
            display: block;
            width: 100%;
            padding: 0.75rem 1rem;
            text-align: left;
            background: none;
            border: none;
            color: var(--secondary);
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s;
            border-bottom: 1px solid var(--border);
        }

        .action-menu-item:last-child {
            border-bottom: none;
        }

        .action-menu-item:hover {
            background: var(--bg);
            color: var(--accent);
        }

        .action-menu-item i {
            width: 20px;
            margin-right: 0.5rem;
            text-align: center;
        }

        .action-menu-item.danger {
            color: var(--danger);
        }

        .action-menu-item.danger:hover {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger);
        }

        /* 列印樣式 */
        @media print {
            .topbar,
            .sidebar,
            .filter-group,
            .action-group,
            .modal-close {
                display: none !important;
            }

            .main-content {
                margin-left: 0 !important;
            }

            .modal {
                position: static !important;
                background: none !important;
            }

            .modal-content {
                box-shadow: none !important;
                margin: 0 !important;
            }
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            padding: 1rem;
            overflow-y: auto;
        }

        .modal-content {
            background: var(--white);
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray);
            cursor: pointer;
            padding: 0.5rem;
        }

        .detail-section {
            margin-bottom: 2rem;
        }

        .detail-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 1rem;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .detail-item {
            padding: 0.75rem;
            background: var(--bg);
            border: 1px solid var(--border);
        }

        .detail-label {
            font-size: 0.75rem;
            color: var(--gray);
            margin-bottom: 0.25rem;
        }

        .detail-value {
            font-weight: 500;
        }

        /* 產品網格 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            padding: 1.5rem;
        }

        .product-item {
            background: var(--bg);
            border: 1px solid var(--border);
            padding: 1.5rem;
            text-align: center;
            transition: all 0.2s;
        }

        .product-item:hover {
            border-color: var(--accent);
            transform: translateY(-2px);
        }

        .product-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .product-price {
            color: var(--accent);
            font-weight: 700;
        }

        /* 側邊欄遮罩 */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 998;
        }

        .sidebar-overlay.show {
            display: block;
        }

        /* 手機版優化 */
        @media (max-width: 768px) {
            .menu-toggle {
                display: block;
            }

            .sidebar {
                transform: translateX(-100%);
                box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .topbar {
                padding: 0 1rem;
            }

            .brand {
                font-size: 1rem;
            }

            .brand-subtitle {
                font-size: 0.55rem;
            }

            .content-wrapper {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .detail-grid {
                grid-template-columns: 1fr;
            }

            .filter-group {
                flex-wrap: wrap;
            }

            .data-table {
                font-size: 0.8rem;
            }

            .data-table th,
            .data-table td {
                padding: 0.75rem;
            }

            .hide-mobile {
                display: none;
            }

            .modal-content {
                margin: 1rem;
                padding: 1.5rem;
            }

            .action-menu-btn {
                width: 28px;
                height: 28px;
                font-size: 0.875rem;
            }
        }

        /* 載入動畫 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: var(--gray);
        }

        .spinner {
            border: 2px solid var(--border);
            border-top: 2px solid var(--accent);
            border-radius: 50%;
            width: 32px;
            height: 32px;
            animation: spin 0.8s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 捲軸樣式 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--light-gray);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gray);
        }
    </style>
</head>
<body>
    <!-- 登入頁面 -->
    <div class="login-page" id="loginPage">
        <div class="login-card">
            <div class="login-header">
                <h1 class="login-title">海水不可斗量</h1>
                <p class="login-subtitle">管理系統登入</p>
            </div>
            <form id="loginForm">
                <div class="form-group">
                    <label class="form-label">帳號</label>
                    <input type="text" class="form-input" id="username" value="admin" required>
                </div>
                <div class="form-group">
                    <label class="form-label">密碼</label>
                    <input type="password" class="form-input" id="password" value="admin123" required>
                </div>
                <button type="submit" class="login-button">登入系統</button>
            </form>
            <p class="login-note">預設帳號：admin / admin123</p>
        </div>
    </div>

    <!-- 主要儀表板 -->
    <div class="dashboard" id="dashboardPage">
        <!-- 頂部導航 -->
        <header class="topbar">
            <div class="topbar-left">
                <button class="menu-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="brand">
                    <span>海水不可斗量</span>
                    <span class="brand-subtitle">THE SEA IS IMMEASURABLE</span>
                </div>
            </div>
            <div class="topbar-right">
                <div class="user-info">
                    <div class="user-avatar">A</div>
                    <span class="user-name">管理員</span>
                </div>
                <a href="#" class="logout-link" onclick="logout()">登出</a>
            </div>
        </header>

        <!-- 側邊欄遮罩 -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- 側邊欄 -->
        <aside class="sidebar" id="sidebar">
            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" onclick="showSection('dashboard')">
                            <i class="fas fa-chart-line nav-icon"></i>
                            <span>儀表板</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('orders')">
                            <i class="fas fa-shopping-cart nav-icon"></i>
                            <span>訂單管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('products')">
                            <i class="fas fa-ice-cream nav-icon"></i>
                            <span>產品管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('analytics')">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span>數據分析</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主要內容 -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- 儀表板頁面 -->
                <div id="dashboardSection">
                    <div class="page-header">
                        <h1 class="page-title">儀表板</h1>
                        <p class="page-subtitle">營運數據總覽</p>
                    </div>

                    <!-- 統計卡片 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-header">
                                <h3 class="stat-title">今日訂單</h3>
                                <i class="fas fa-shopping-bag stat-icon"></i>
                            </div>
                            <div class="stat-value" id="todayOrders">0</div>
                            <div class="stat-change positive">+12.5% 較昨日</div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-header">
                                <h3 class="stat-title">今日營收</h3>
                                <i class="fas fa-dollar-sign stat-icon"></i>
                            </div>
                            <div class="stat-value" id="todayRevenue">NT$ 0</div>
                            <div class="stat-change positive">+8.3% 較昨日</div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-header">
                                <h3 class="stat-title">待處理訂單</h3>
                                <i class="fas fa-clock stat-icon"></i>
                            </div>
                            <div class="stat-value" id="pendingOrders">0</div>
                            <div class="stat-change">需要處理</div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-header">
                                <h3 class="stat-title">產品總數</h3>
                                <i class="fas fa-ice-cream stat-icon"></i>
                            </div>
                            <div class="stat-value" id="totalProducts">40</div>
                            <div class="stat-change">10 基底 + 30 配料</div>
                        </div>
                    </div>

                    <!-- 最新訂單 -->
                    <div class="data-card">
                        <div class="card-header">
                            <h2 class="card-title">最新訂單</h2>
                            <button class="filter-btn" onclick="showSection('orders')">查看全部</button>
                        </div>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>訂單資訊</th>
                                    <th class="hide-mobile">品項</th>
                                    <th>金額</th>
                                    <th>狀態</th>
                                    <th class="hide-mobile">時間</th>
                                </tr>
                            </thead>
                            <tbody id="recentOrdersTable">
                                <tr>
                                    <td colspan="6" class="loading">
                                        <div class="spinner"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 訂單管理頁面 -->
                <div id="ordersSection" style="display: none;">
                    <div class="page-header">
                        <h1 class="page-title">訂單管理</h1>
                        <p class="page-subtitle">處理與追蹤所有訂單</p>
                    </div>

                    <div class="data-card">
                        <div class="card-header">
                            <h2 class="card-title">訂單列表</h2>
                            <div class="filter-group">
                                <button class="filter-btn active" onclick="filterOrders('all')">全部</button>
                                <button class="filter-btn" onclick="filterOrders('pending')">待處理</button>
                                <button class="filter-btn" onclick="filterOrders('preparing')">準備中</button>
                                <button class="filter-btn" onclick="filterOrders('ready')">待取貨</button>
                                <button class="filter-btn" onclick="filterOrders('delivered')">已完成</button>
                            </div>
                        </div>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>操作</th>
                                    <th>訂單資訊</th>
                                    <th class="hide-mobile">品項</th>
                                    <th>金額</th>
                                    <th>付款</th>
                                    <th>狀態</th>
                                    <th class="hide-mobile">時間</th>
                                </tr>
                            </thead>
                            <tbody id="ordersTable">
                                <tr>
                                    <td colspan="8" class="loading">
                                        <div class="spinner"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 產品管理頁面 -->
                <div id="productsSection" style="display: none;">
                    <div class="page-header">
                        <h1 class="page-title">產品管理</h1>
                        <p class="page-subtitle">管理基底口味與配料選項</p>
                    </div>

                    <div class="data-card">
                        <div class="card-header">
                            <h2 class="card-title">基底口味（10種）</h2>
                        </div>
                        <div class="product-grid" id="basesGrid">
                            <div class="loading">
                                <div class="spinner"></div>
                            </div>
                        </div>
                    </div>

                    <div class="data-card">
                        <div class="card-header">
                            <h2 class="card-title">配料選項（30種）</h2>
                        </div>
                        <div class="product-grid" id="toppingsGrid">
                            <div class="loading">
                                <div class="spinner"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 數據分析頁面 -->
                <div id="analyticsSection" style="display: none;">
                    <div class="page-header">
                        <h1 class="page-title">數據分析</h1>
                        <p class="page-subtitle">營運數據與趨勢分析</p>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-header">
                                <h3 class="stat-title">本月營收</h3>
                                <i class="fas fa-chart-line stat-icon"></i>
                            </div>
                            <div class="stat-value">NT$ 285,420</div>
                            <div class="stat-change positive">+23.5% 成長</div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-header">
                                <h3 class="stat-title">本月訂單</h3>
                                <i class="fas fa-receipt stat-icon"></i>
                            </div>
                            <div class="stat-value">1,234</div>
                            <div class="stat-change positive">+18.2% 成長</div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-header">
                                <h3 class="stat-title">平均客單價</h3>
                                <i class="fas fa-calculator stat-icon"></i>
                            </div>
                            <div class="stat-value">NT$ 231</div>
                            <div class="stat-change positive">+5.3% 提升</div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-header">
                                <h3 class="stat-title">熱門口味</h3>
                                <i class="fas fa-trophy stat-icon"></i>
                            </div>
                            <div class="stat-value">比利時巧克力</div>
                            <div class="stat-change">本月銷售冠軍</div>
                        </div>
                    </div>

                    <div class="data-card">
                        <div class="card-header">
                            <h2 class="card-title">營收趨勢圖</h2>
                        </div>
                        <div style="padding: 4rem; text-align: center; color: var(--gray);">
                            <i class="fas fa-chart-area" style="font-size: 3rem; opacity: 0.3;"></i>
                            <p style="margin-top: 1rem;">圖表功能開發中</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 訂單詳情 Modal -->
    <div class="modal" id="orderModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">訂單詳情</h2>
                <button class="modal-close" onclick="closeOrderModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="orderDetails">
                <!-- 訂單詳情將動態載入 -->
            </div>
        </div>
    </div>

    <script>
        // API 設定
        const API_BASE = 'http://localhost:5000/api';
        
        // 狀態管理
        let currentUser = null;
        let orders = [];
        let products = { bases: [], toppings: [] };
        let currentFilter = 'all';

        // 登入處理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.user;
                    localStorage.setItem('token', data.token);
                    showDashboard();
                } else {
                    handleLoginError();
                }
            } catch (error) {
                // 模擬登入
                if (username === 'admin' && password === 'admin123') {
                    currentUser = { username: 'admin', role: 'admin' };
                    showDashboard();
                } else {
                    alert('登入失敗：帳號或密碼錯誤');
                }
            }
        });

        // 顯示儀表板
        function showDashboard() {
            document.getElementById('loginPage').style.display = 'none';
            document.getElementById('dashboardPage').style.display = 'block';
            loadDashboardData();
        }

        // 登出
        function logout() {
            currentUser = null;
            localStorage.removeItem('token');
            document.getElementById('loginPage').style.display = 'flex';
            document.getElementById('dashboardPage').style.display = 'none';
        }

        // 切換側邊欄
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            
            sidebar.classList.toggle('open');
            
            // 手機版顯示/隱藏遮罩
            if (window.innerWidth <= 768) {
                overlay.classList.toggle('show');
            }
        }

        // 載入儀表板資料
        async function loadDashboardData() {
            await loadOrders();
            await loadProducts();
            updateStats();
            renderRecentOrders();
        }

        // 載入訂單
        async function loadOrders() {
            try {
                const response = await fetch(`${API_BASE}/orders`);
                if (response.ok) {
                    orders = await response.json();
                } else {
                    throw new Error('Failed to load orders');
                }
            } catch (error) {
                console.log('Using mock orders');
                // 模擬訂單資料
                orders = [
                    {
                        _id: '1',
                        orderNumber: 'ORD202401001',
                        customer: {
                            name: '林小姐',
                            phone: '0912-345-678',
                            email: '<EMAIL>',
                            address: '台北市信義區信義路五段7號'
                        },
                        items: [{
                            base: { name: '比利時巧克力' },
                            toppings: [{ name: '比利時巧克力醬' }, { name: 'Oreo巧克力餅' }],
                            quantity: 1,
                            itemPrice: 120
                        }],
                        totalAmount: 120,
                        status: 'pending',
                        paymentMethod: 'card',
                        createdAt: new Date().toISOString()
                    },
                    {
                        _id: '2',
                        orderNumber: 'ORD202401002',
                        customer: {
                            name: '陳先生',
                            phone: '0923-456-789',
                            email: '<EMAIL>',
                            address: '台北市大安區忠孝東路四段100號'
                        },
                        items: [{
                            base: { name: '宇治抹茶' },
                            toppings: [{ name: '抹茶粉' }, { name: '紅豆' }],
                            quantity: 2,
                            itemPrice: 125
                        }],
                        totalAmount: 250,
                        status: 'preparing',
                        paymentMethod: 'cash',
                        createdAt: new Date(Date.now() - 3600000).toISOString()
                    }
                ];
            }
        }

        // 載入產品
        async function loadProducts() {
            try {
                const response = await fetch(`${API_BASE}/products`);
                if (response.ok) {
                    const allProducts = await response.json();
                    products.bases = allProducts.filter(p => p.type === 'base');
                    products.toppings = allProducts.filter(p => p.type === 'topping');
                } else {
                    throw new Error('Failed to load products');
                }
            } catch (error) {
                console.log('Using mock products');
                await initializeProducts();
            }
        }

        // 初始化產品資料
        async function initializeProducts() {
            try {
                await fetch(`${API_BASE}/products/init-samples`, { method: 'POST' });
                await loadProducts();
            } catch (error) {
                // 使用本地模擬資料
                products.bases = [
                    { name: '經典香草', price: 80 },
                    { name: '比利時巧克力', price: 85 },
                    { name: '新鮮草莓', price: 85 },
                    { name: '宇治抹茶', price: 90 },
                    { name: '愛文芒果', price: 90 },
                    { name: '薄荷巧克力', price: 95 },
                    { name: '焦糖海鹽', price: 95 },
                    { name: '金枕頭榴槤', price: 100 },
                    { name: '大甲芋頭', price: 85 },
                    { name: '西西里檸檬', price: 80 }
                ];
                products.toppings = Array(30).fill(null).map((_, i) => ({
                    name: `精選配料${i + 1}`,
                    price: 15 + Math.floor(Math.random() * 20)
                }));
            }
        }

        // 更新統計數據
        function updateStats() {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            const todayOrders = orders.filter(order => 
                new Date(order.createdAt) >= today
            );
            
            const todayRevenue = todayOrders.reduce((sum, order) => 
                sum + order.totalAmount, 0
            );
            
            const pendingOrders = orders.filter(order => 
                order.status === 'pending'
            );
            
            document.getElementById('todayOrders').textContent = todayOrders.length;
            document.getElementById('todayRevenue').textContent = `NT$ ${todayRevenue.toLocaleString()}`;
            document.getElementById('pendingOrders').textContent = pendingOrders.length;
            document.getElementById('totalProducts').textContent = 
                products.bases.length + products.toppings.length;
        }

        // 渲染最新訂單
        function renderRecentOrders() {
            const recentOrders = orders.slice(0, 5);
            const tbody = document.getElementById('recentOrdersTable');
            
            if (recentOrders.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; color: var(--gray);">
                            暫無訂單
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = recentOrders.map(order => `
                <tr>
                    <td>
                        <div style="font-weight: 600;">${order.orderNumber}</div>
                        <div>${order.customer.name}</div>
                        <div style="font-size: 0.8rem; color: var(--gray);">${order.customer.phone}</div>
                    </td>
                    <td class="hide-mobile">${getOrderItemsText(order.items)}</td>
                    <td style="font-weight: 600;">NT$ ${order.totalAmount}</td>
                    <td><span class="status ${order.status}">${getStatusText(order.status)}</span></td>
                    <td class="hide-mobile">${formatTime(order.createdAt)}</td>
                </tr>
            `).join('');
        }

        // 切換頁面區塊
        function showSection(section) {
            // 隱藏所有區塊
            document.getElementById('dashboardSection').style.display = 'none';
            document.getElementById('ordersSection').style.display = 'none';
            document.getElementById('productsSection').style.display = 'none';
            document.getElementById('analyticsSection').style.display = 'none';
            
            // 更新選單狀態
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.closest('.nav-link').classList.add('active');
            
            // 顯示選定區塊
            switch(section) {
                case 'dashboard':
                    document.getElementById('dashboardSection').style.display = 'block';
                    break;
                case 'orders':
                    document.getElementById('ordersSection').style.display = 'block';
                    renderOrdersTable();
                    break;
                case 'products':
                    document.getElementById('productsSection').style.display = 'block';
                    renderProducts();
                    break;
                case 'analytics':
                    document.getElementById('analyticsSection').style.display = 'block';
                    break;
            }
            
            // 手機版關閉側邊欄
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('open');
                document.getElementById('sidebarOverlay').classList.remove('show');
            }
        }

        // 渲染訂單表格
        function renderOrdersTable() {
            const filteredOrders = currentFilter === 'all' 
                ? orders 
                : orders.filter(order => order.status === currentFilter);
            
            const tbody = document.getElementById('ordersTable');
            
            if (filteredOrders.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; color: var(--gray);">
                            暫無訂單
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = filteredOrders.map(order => `
                <tr>
                    <td>
                        <div class="action-group">
                            <button class="action-menu-btn" onclick="toggleActionMenu(event, '${order._id}')">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action-menu" id="menu-${order._id}">
                                <button class="action-menu-item" onclick="viewOrderDetails('${order._id}')">
                                    <i class="fas fa-eye"></i> 查看詳情
                                </button>
                                <button class="action-menu-item" onclick="updateOrderStatus('${order._id}')">
                                    <i class="fas fa-edit"></i> 更新狀態
                                </button>
                                <button class="action-menu-item" onclick="printOrder('${order._id}')">
                                    <i class="fas fa-print"></i> 列印訂單
                                </button>
                                <button class="action-menu-item" onclick="contactCustomer('${order._id}')">
                                    <i class="fas fa-phone"></i> 聯絡客戶
                                </button>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div style="font-weight: 600;">${order.orderNumber}</div>
                        <div>${order.customer.name}</div>
                        <div style="font-size: 0.8rem; color: var(--gray);">${order.customer.phone}</div>
                    </td>
                    <td class="hide-mobile">${getOrderItemsText(order.items)}</td>
                    <td style="font-weight: 600;">NT$ ${order.totalAmount}</td>
                    <td>${getPaymentMethodText(order.paymentMethod)}</td>
                    <td><span class="status ${order.status}">${getStatusText(order.status)}</span></td>
                    <td class="hide-mobile">${formatTime(order.createdAt)}</td>
                </tr>
            `).join('');
        }

        // 篩選訂單
        function filterOrders(filter) {
            currentFilter = filter;
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            renderOrdersTable();
        }

        // 渲染產品
        function renderProducts() {
            // 渲染基底
            const basesGrid = document.getElementById('basesGrid');
            basesGrid.innerHTML = products.bases.map(base => `
                <div class="product-item">
                    <div class="product-name">${base.name}</div>
                    <div class="product-price">NT$ ${base.price}</div>
                </div>
            `).join('');
            
            // 渲染配料
            const toppingsGrid = document.getElementById('toppingsGrid');
            toppingsGrid.innerHTML = products.toppings.map(topping => `
                <div class="product-item">
                    <div class="product-name">${topping.name}</div>
                    <div class="product-price">+NT$ ${topping.price}</div>
                </div>
            `).join('');
        }

        // 查看訂單詳情
        function viewOrderDetails(orderId) {
            const order = orders.find(o => o._id === orderId);
            if (!order) return;
            
            // 關閉選單
            document.querySelectorAll('.action-menu').forEach(menu => {
                menu.classList.remove('show');
            });
            
            const detailsHTML = `
                <div class="detail-section">
                    <h3 class="detail-title">訂單資訊</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">訂單編號</div>
                            <div class="detail-value">${order.orderNumber}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">下單時間</div>
                            <div class="detail-value">${formatTime(order.createdAt)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">訂單狀態</div>
                            <div class="detail-value">
                                <span class="status ${order.status}">${getStatusText(order.status)}</span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">付款方式</div>
                            <div class="detail-value">${getPaymentMethodText(order.paymentMethod)}</div>
                        </div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h3 class="detail-title">顧客資訊</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">姓名</div>
                            <div class="detail-value">${order.customer.name}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">電話</div>
                            <div class="detail-value">${order.customer.phone}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Email</div>
                            <div class="detail-value">${order.customer.email}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">地址</div>
                            <div class="detail-value">${order.customer.address}</div>
                        </div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h3 class="detail-title">訂購品項</h3>
                    ${order.items.map(item => `
                        <div style="padding: 1rem; background: var(--bg); border: 1px solid var(--border); margin-bottom: 1rem;">
                            <div style="font-weight: 600; margin-bottom: 0.5rem;">
                                基底：${item.base.name}
                            </div>
                            ${item.toppings.length > 0 ? `
                                <div style="color: var(--gray); font-size: 0.9rem;">
                                    配料：${item.toppings.map(t => t.name).join('、')}
                                </div>
                            ` : ''}
                            <div style="margin-top: 0.5rem; display: flex; justify-content: space-between;">
                                <span>數量：${item.quantity}</span>
                                <span style="font-weight: 600;">NT$ ${item.itemPrice}</span>
                            </div>
                        </div>
                    `).join('')}
                    <div style="text-align: right; padding-top: 1rem; border-top: 1px solid var(--border);">
                        <strong style="font-size: 1.25rem;">總計：NT$ ${order.totalAmount}</strong>
                    </div>
                </div>
            `;
            
            document.getElementById('orderDetails').innerHTML = detailsHTML;
            document.getElementById('orderModal').style.display = 'block';
        }

        // 關閉訂單詳情
        function closeOrderModal() {
            document.getElementById('orderModal').style.display = 'none';
        }

        // 更新訂單狀態
        async function updateOrderStatus(orderId) {
            const order = orders.find(o => o._id === orderId);
            if (!order) return;
            
            // 關閉選單
            document.querySelectorAll('.action-menu').forEach(menu => {
                menu.classList.remove('show');
            });
            
            const statusFlow = {
                'pending': 'confirmed',
                'confirmed': 'preparing',
                'preparing': 'ready',
                'ready': 'delivered'
            };
            
            const nextStatus = statusFlow[order.status];
            if (!nextStatus) {
                alert('此訂單已完成');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/orders/${orderId}/status`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({ status: nextStatus })
                });
                
                if (response.ok) {
                    order.status = nextStatus;
                    renderOrdersTable();
                    updateStats();
                    alert(`訂單狀態已更新為：${getStatusText(nextStatus)}`);
                }
            } catch (error) {
                // 模擬更新
                order.status = nextStatus;
                renderOrdersTable();
                updateStats();
                alert(`訂單狀態已更新為：${getStatusText(nextStatus)}`);
            }
        }

        // 輔助函數
        function getOrderItemsText(items) {
            return items.map(item => {
                const toppings = item.toppings.map(t => t.name).join('、');
                return `${item.base.name}${toppings ? ` + ${toppings}` : ''}`;
            }).join('; ');
        }

        function getStatusText(status) {
            const statusMap = {
                'pending': '待處理',
                'confirmed': '已確認',
                'preparing': '準備中',
                'ready': '待取貨',
                'delivered': '已完成',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        function getPaymentMethodText(method) {
            const methodMap = {
                'cash': '現金',
                'card': '信用卡',
                'online': '轉帳'
            };
            return methodMap[method] || method;
        }

        function formatTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 3600000) {
                return `${Math.floor(diff / 60000)} 分鐘前`;
            } else if (diff < 86400000) {
                return `${Math.floor(diff / 3600000)} 小時前`;
            } else {
                return date.toLocaleDateString('zh-TW');
            }
        }

        // 初始化
        window.onload = function() {
            // 初始化管理員帳號
            fetch(`${API_BASE}/auth/init-admin`, { method: 'POST' })
                .catch(() => console.log('Admin initialization skipped'));
        };

        // 點擊 modal 外部關閉
        document.getElementById('orderModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeOrderModal();
            }
        });

        // 切換操作選單
        function toggleActionMenu(event, orderId) {
            event.stopPropagation();
            const menu = document.getElementById(`menu-${orderId}`);
            const allMenus = document.querySelectorAll('.action-menu');
            
            // 關閉其他選單
            allMenus.forEach(m => {
                if (m !== menu) {
                    m.classList.remove('show');
                }
            });
            
            // 切換當前選單
            menu.classList.toggle('show');
        }

        // 點擊其他地方關閉選單
        document.addEventListener('click', function() {
            document.querySelectorAll('.action-menu').forEach(menu => {
                menu.classList.remove('show');
            });
        });

        // 列印訂單
        function printOrder(orderId) {
            const order = orders.find(o => o._id === orderId);
            if (!order) return;
            
            // 關閉選單
            document.querySelectorAll('.action-menu').forEach(menu => {
                menu.classList.remove('show');
            });
            
            // 顯示訂單詳情並列印
            viewOrderDetails(orderId);
            setTimeout(() => {
                window.print();
            }, 500);
        }

        // 聯絡客戶
        function contactCustomer(orderId) {
            const order = orders.find(o => o._id === orderId);
            if (!order) return;
            
            // 關閉選單
            document.querySelectorAll('.action-menu').forEach(menu => {
                menu.classList.remove('show');
            });
            
            // 顯示聯絡資訊
            alert(`客戶聯絡資訊：\n\n姓名：${order.customer.name}\n電話：${order.customer.phone}\nEmail：${order.customer.email}`);
        }
    </script>
</body>
</html> 