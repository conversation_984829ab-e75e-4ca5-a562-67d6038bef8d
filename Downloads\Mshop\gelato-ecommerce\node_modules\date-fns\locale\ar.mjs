import { formatDistance } from "./ar/_lib/formatDistance.mjs";
import { formatLong } from "./ar/_lib/formatLong.mjs";
import { formatRelative } from "./ar/_lib/formatRelative.mjs";
import { localize } from "./ar/_lib/localize.mjs";
import { match } from "./ar/_lib/match.mjs";

/**
 * @category Locales
 * @summary Arabic locale (Modern Standard Arabic - Al-fussha).
 * @language Modern Standard Arabic
 * @iso-639-2 ara
 * <AUTHOR> [@AbdallahAHO](https://github.com/AbdallahAHO)
 * <AUTHOR> [@essana3](https://github.com/essana3)
 */
export const ar = {
  code: "ar",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 6 /* Saturday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default ar;
