"use strict";
exports.kn = void 0;
var _index = require("./kn/_lib/formatDistance.js");
var _index2 = require("./kn/_lib/formatLong.js");
var _index3 = require("./kn/_lib/formatRelative.js");
var _index4 = require("./kn/_lib/localize.js");
var _index5 = require("./kn/_lib/match.js");

/**
 * @category Locales
 * @summary Kannada locale (India).
 * @language Kannada
 * @iso-639-2 kan
 * <AUTHOR> [@developergouli](https://github.com/developergouli)
 */
const kn = (exports.kn = {
  code: "kn",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
